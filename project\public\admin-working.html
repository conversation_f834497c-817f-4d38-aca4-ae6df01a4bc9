<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindEase Admin Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        .login-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .admin-title {
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
        }
        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 10px;
        }
        .emergency-btn {
            width: 100%;
            padding: 14px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
        }
        .admin-dashboard {
            display: none;
            min-height: 100vh;
            background: #f5f5f5;
        }
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .dashboard-content {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 16px;
            color: #666;
        }
        .section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="admin-title">🧠 MindEase Admin Portal</div>
            
            <div class="form-group">
                <label>Admin Email</label>
                <input type="email" id="email" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label>Password</label>
                <input type="password" id="password" value="MindEase@Admin2024">
            </div>
            
            <button class="login-btn" onclick="login()">Login to Dashboard</button>
            <button class="emergency-btn" onclick="login()">🚀 EMERGENCY LOGIN</button>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="adminDashboard" class="admin-dashboard">
        <div class="dashboard-header">
            <h1>MindEase Admin Dashboard</h1>
            <button class="logout-btn" onclick="logout()">Logout</button>
        </div>
        
        <div class="dashboard-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalUsers">0</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalTherapists">0</div>
                    <div class="stat-label">Total Therapists</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalAppointments">0</div>
                    <div class="stat-label">Total Appointments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalRevenue">$0</div>
                    <div class="stat-label">Total Revenue</div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">Recent Users</div>
                <div id="usersTable">Loading users...</div>
            </div>

            <div class="section">
                <div class="section-title">Recent Therapists</div>
                <div id="therapistsTable">Loading therapists...</div>
            </div>

            <div class="section">
                <div class="section-title">Recent Appointments</div>
                <div id="appointmentsTable">Loading appointments...</div>
            </div>
        </div>
    </div>

    <script>
        // Simple login function
        function login() {
            console.log('Login function called!');
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            console.log('Email:', email);
            console.log('Password:', password);
            
            if (email === '<EMAIL>' && password === 'MindEase@Admin2024') {
                console.log('Login successful!');
                
                // Hide login screen
                document.getElementById('loginScreen').style.display = 'none';
                // Show dashboard
                document.getElementById('adminDashboard').style.display = 'block';
                
                // Load data
                loadData();
            } else {
                alert('Invalid credentials!');
            }
        }

        // Logout function
        function logout() {
            document.getElementById('adminDashboard').style.display = 'none';
            document.getElementById('loginScreen').style.display = 'flex';
        }

        // Load data function
        async function loadData() {
            console.log('Loading data...');
            
            try {
                // Load users
                const usersResponse = await fetch('/api/users');
                const users = await usersResponse.json();
                document.getElementById('totalUsers').textContent = users.length;
                
                let usersTable = '<table><tr><th>Username</th><th>Email</th><th>Created</th></tr>';
                users.slice(0, 10).forEach(user => {
                    usersTable += `<tr><td>${user.username || 'N/A'}</td><td>${user.email}</td><td>${new Date(user.createdAt).toLocaleDateString()}</td></tr>`;
                });
                usersTable += '</table>';
                document.getElementById('usersTable').innerHTML = usersTable;

                // Load therapists
                const therapistsResponse = await fetch('/api/therapists');
                const therapists = await therapistsResponse.json();
                document.getElementById('totalTherapists').textContent = therapists.length;
                
                let therapistsTable = '<table><tr><th>Name</th><th>Email</th><th>Specialization</th></tr>';
                therapists.slice(0, 10).forEach(therapist => {
                    therapistsTable += `<tr><td>${therapist.username || therapist.name || 'N/A'}</td><td>${therapist.email}</td><td>${therapist.specialization || 'General'}</td></tr>`;
                });
                therapistsTable += '</table>';
                document.getElementById('therapistsTable').innerHTML = therapistsTable;

                // Load appointments
                const appointmentsResponse = await fetch('/api/appointments');
                const appointments = await appointmentsResponse.json();
                document.getElementById('totalAppointments').textContent = appointments.length;
                
                // Calculate revenue
                let totalRevenue = 0;
                appointments.forEach(apt => {
                    if (apt.fee) totalRevenue += parseFloat(apt.fee);
                });
                document.getElementById('totalRevenue').textContent = '$' + totalRevenue.toFixed(2);
                
                let appointmentsTable = '<table><tr><th>User</th><th>Therapist</th><th>Date</th><th>Status</th></tr>';
                appointments.slice(0, 10).forEach(apt => {
                    appointmentsTable += `<tr><td>${apt.userEmail || 'N/A'}</td><td>${apt.therapistEmail || 'N/A'}</td><td>${apt.appointmentDate ? new Date(apt.appointmentDate).toLocaleDateString() : 'N/A'}</td><td>${apt.status || 'Pending'}</td></tr>`;
                });
                appointmentsTable += '</table>';
                document.getElementById('appointmentsTable').innerHTML = appointmentsTable;

                console.log('Data loaded successfully!');
            } catch (error) {
                console.error('Error loading data:', error);
            }
        }

        // Test function to make sure JavaScript is working
        console.log('JavaScript is loaded and working!');
        
        // Add click event listener as backup
        window.onload = function() {
            console.log('Page loaded!');
        };
    </script>
</body>
</html>
