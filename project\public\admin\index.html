<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindEase Admin Portal</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-card {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
        }

        .logo-icon i {
            font-size: 40px;
            color: white;
        }

        .logo-text {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .logo-subtitle {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 18px;
        }

        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #c33;
        }

        .admin-dashboard {
            display: none;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-title {
            font-size: 24px;
            font-weight: bold;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        .header-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .header-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .dashboard-content {
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            gap: 20px;
            padding: 20px;
        }

        .sidebar {
            width: 250px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            height: fit-content;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 5px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
        }

        .sidebar-item:hover {
            background: #f8f9ff;
            color: #667eea;
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .sidebar-item i {
            margin-right: 12px;
            width: 20px;
        }

        .main-content {
            flex: 1;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 16px;
            opacity: 0.9;
        }

        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .data-table th {
            background: #f8f9ff;
            font-weight: 600;
            color: #333;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
        }

        .btn-edit {
            background: #007bff;
            color: white;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        @media (max-width: 768px) {
            .dashboard-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="logo-section">
                <div class="logo-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <div class="logo-text">MindEase</div>
                <div class="logo-subtitle">Admin Portal</div>
            </div>

            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Admin Email</label>
                    <input type="email" id="email" required value="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="password-container">
                        <input type="password" id="password" required value="MindEase@Admin2024">
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i> Login to Dashboard
                </button>
            </form>

            <!-- Debug button -->
            <button onclick="debugLogin()" style="margin-top: 10px; width: 100%; padding: 10px; background: #28a745; color: white; border: none; border-radius: 8px;">
                🔧 Debug Login (Click if login button doesn't work)
            </button>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="adminDashboard" class="admin-dashboard">
        <div class="dashboard-header">
            <div class="header-content">
                <div class="header-title" id="headerTitle">Dashboard Overview</div>
                <div class="header-actions">
                    <button class="header-btn" onclick="showNotifications()">
                        <i class="fas fa-bell"></i>
                    </button>
                    <button class="header-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            </div>
        </div>

        <div class="dashboard-content">
            <div class="sidebar">
                <div class="sidebar-item active" onclick="showSection('overview')">
                    <i class="fas fa-chart-line"></i>
                    Dashboard Overview
                </div>
                <div class="sidebar-item" onclick="showSection('users')">
                    <i class="fas fa-users"></i>
                    User Management
                </div>
                <div class="sidebar-item" onclick="showSection('therapists')">
                    <i class="fas fa-user-md"></i>
                    Therapist Management
                </div>
                <div class="sidebar-item" onclick="showSection('appointments')">
                    <i class="fas fa-calendar-alt"></i>
                    Appointment Management
                </div>
                <div class="sidebar-item" onclick="showSection('payments')">
                    <i class="fas fa-credit-card"></i>
                    Payment Management
                </div>
            </div>

            <div class="main-content">
                <!-- Overview Section -->
                <div id="overviewSection" class="content-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="totalUsers">0</div>
                            <div class="stat-label">Total Users</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="activeTherapists">0</div>
                            <div class="stat-label">Active Therapists</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="todayAppointments">0</div>
                            <div class="stat-label">Today's Appointments</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalRevenue">$0</div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                    </div>

                    <div class="section-title">Recent Activity</div>
                    <div id="recentActivity" class="loading">Loading recent activity...</div>
                </div>

                <!-- Users Section -->
                <div id="usersSection" class="content-section" style="display: none;">
                    <div class="section-title">User Management</div>
                    <div id="usersTable" class="loading">Loading users...</div>
                </div>

                <!-- Therapists Section -->
                <div id="therapistsSection" class="content-section" style="display: none;">
                    <div class="section-title">Therapist Management</div>
                    <div id="therapistsTable" class="loading">Loading therapists...</div>
                </div>

                <!-- Appointments Section -->
                <div id="appointmentsSection" class="content-section" style="display: none;">
                    <div class="section-title">Appointment Management</div>
                    <div id="appointmentsTable" class="loading">Loading appointments...</div>
                </div>

                <!-- Payments Section -->
                <div id="paymentsSection" class="content-section" style="display: none;">
                    <div class="section-title">Payment Management</div>
                    <div id="paymentsTable" class="loading">Loading payments...</div>
                </div>
            </div>
        </div>
    </div>

    <script src="admin.js"></script>
</body>
</html>
