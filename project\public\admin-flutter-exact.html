<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindEase Admin Portal</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #6B73FF 100%);
            min-height: 100vh;
        }

        /* Login Screen Styles */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 24px;
        }

        .login-card {
            background: white;
            padding: 40px;
            border-radius: 24px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.15), 0 5px 15px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
        }

        .admin-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .admin-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }

        .admin-icon i {
            font-size: 40px;
            color: white;
        }

        .admin-title {
            font-size: 32px;
            font-weight: bold;
            color: #1A237E;
            margin-bottom: 8px;
        }

        .admin-subtitle {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 16px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 18px;
        }

        .login-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #c33;
            font-size: 14px;
        }

        /* Dashboard Styles */
        .admin-dashboard {
            display: none;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 24px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .header-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .header-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .dashboard-content {
            display: flex;
            min-height: calc(100vh - 72px);
        }

        .sidebar {
            width: 280px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            background: linear-gradient(135deg, #1A237E 0%, #3949AB 100%);
            color: white;
            padding: 24px;
        }

        .sidebar-icon {
            width: 56px;
            height: 56px;
            background: rgba(255,255,255,0.2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
        }

        .sidebar-icon i {
            font-size: 32px;
            color: white;
        }

        .sidebar-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .sidebar-subtitle {
            font-size: 14px;
            color: rgba(255,255,255,0.7);
        }

        .sidebar-menu {
            padding: 0;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }

        .sidebar-item:hover {
            background: #f8f9ff;
        }

        .sidebar-item.active {
            background: rgba(26, 35, 126, 0.1);
            border-right: 4px solid #1A237E;
        }

        .sidebar-item i {
            margin-right: 16px;
            width: 20px;
            color: #666;
        }

        .sidebar-item.active i {
            color: #1A237E;
        }

        .sidebar-item span {
            color: #333;
            font-weight: 500;
        }

        .sidebar-item.active span {
            color: #1A237E;
            font-weight: bold;
        }

        .main-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
        }

        .stat-icon i {
            font-size: 24px;
            color: white;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #1A237E;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #1A237E;
            margin-bottom: 16px;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .content-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .dashboard-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="admin-header">
                <div class="admin-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <div class="admin-title">MindEase Admin</div>
                <div class="admin-subtitle">Administrative Panel</div>
            </div>

            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Admin Email</label>
                    <input type="email" id="email" required value="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="password-container">
                        <input type="password" id="password" required value="MindEase@Admin2024">
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="login-btn" id="loginBtn" onclick="doLogin(); return false;">
                    <i class="fas fa-sign-in-alt"></i> Login to Dashboard
                </button>
            </form>

            <!-- Emergency login button -->
            <button onclick="doLogin()" style="width: 100%; margin-top: 10px; padding: 16px; background: #28a745; color: white; border: none; border-radius: 12px; font-size: 16px; font-weight: 600; cursor: pointer;">
                🚀 EMERGENCY LOGIN (Click if above doesn't work)
            </button>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="adminDashboard" class="admin-dashboard">
        <div class="dashboard-header">
            <div class="header-title" id="headerTitle">Dashboard Overview</div>
            <div class="header-actions">
                <button class="header-btn" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                </button>
                <button class="header-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>

        <div class="dashboard-content">
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="sidebar-title">MindEase Admin</div>
                    <div class="sidebar-subtitle">Administrative Panel</div>
                </div>
                
                <div class="sidebar-menu">
                    <button class="sidebar-item active" onclick="showSection('overview', this)">
                        <i class="fas fa-chart-line"></i>
                        <span>Dashboard</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('users', this)">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('therapists', this)">
                        <i class="fas fa-user-md"></i>
                        <span>Therapists</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('appointments', this)">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Appointments</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('payments', this)">
                        <i class="fas fa-credit-card"></i>
                        <span>Payments</span>
                    </button>
                </div>
            </div>

            <div class="main-content">
                <!-- Overview Section -->
                <div id="overviewSection" class="content-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-number" id="totalUsers">0</div>
                            <div class="stat-label">Total Users</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="stat-number" id="activeTherapists">0</div>
                            <div class="stat-label">Active Therapists</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stat-number" id="todayAppointments">0</div>
                            <div class="stat-label">Today's Appointments</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-number" id="totalRevenue">$0</div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                    </div>

                    <div class="section-title">Recent Activity</div>
                    <div id="recentActivity" class="loading">
                        <div class="spinner"></div>
                        Loading recent activity...
                    </div>
                </div>

                <!-- Users Section -->
                <div id="usersSection" class="content-section" style="display: none;">
                    <div class="section-title">User Management</div>
                    <div id="usersTable" class="loading">
                        <div class="spinner"></div>
                        Loading users...
                    </div>
                </div>

                <!-- Therapists Section -->
                <div id="therapistsSection" class="content-section" style="display: none;">
                    <div class="section-title">Therapist Management</div>
                    <div id="therapistsTable" class="loading">
                        <div class="spinner"></div>
                        Loading therapists...
                    </div>
                </div>

                <!-- Appointments Section -->
                <div id="appointmentsSection" class="content-section" style="display: none;">
                    <div class="section-title">Appointment Management</div>
                    <div id="appointmentsTable" class="loading">
                        <div class="spinner"></div>
                        Loading appointments...
                    </div>
                </div>

                <!-- Payments Section -->
                <div id="paymentsSection" class="content-section" style="display: none;">
                    <div class="section-title">Payment Management</div>
                    <div id="paymentsTable" class="loading">
                        <div class="spinner"></div>
                        Loading payments...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Exact Flutter Admin Portal Implementation
        const API_BASE_URL = '/api';

        // Admin credentials (exactly like Flutter)
        const ADMIN_EMAIL = "<EMAIL>";
        const ADMIN_PASSWORD = "MindEase@Admin2024";

        // Simple login function that works
        function doLogin() {
            console.log('Login button clicked!');

            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;

            console.log('Email:', email);
            console.log('Password:', password);
            console.log('Expected email:', ADMIN_EMAIL);
            console.log('Expected password:', ADMIN_PASSWORD);

            if (email === ADMIN_EMAIL && password === ADMIN_PASSWORD) {
                console.log('Credentials match! Logging in...');

                // Hide login screen
                document.getElementById('loginScreen').style.display = 'none';
                // Show dashboard
                document.getElementById('adminDashboard').style.display = 'block';

                console.log('Dashboard should be visible now');

                // Load dashboard data
                loadDashboardData();
            } else {
                console.log('Invalid credentials');
                const errorMessage = document.getElementById('errorMessage');
                errorMessage.textContent = 'Invalid admin credentials';
                errorMessage.style.display = 'block';
            }
        }

        // Login functionality (exactly like Flutter)
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up login...');

            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');

            if (loginForm && loginBtn) {
                console.log('Login form and button found');

                // Add click event to button
                loginBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Login button clicked via event listener');
                    doLogin();
                });

                // Add form submit event
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    console.log('Form submitted');
                    doLogin();
                });
            } else {
                console.error('Login form or button not found!');
            }
        });

        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.querySelector('.password-toggle i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Show different sections (exactly like Flutter drawer navigation)
        function showSection(section, element) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(el => {
                el.style.display = 'none';
            });

            // Remove active class from all sidebar items
            document.querySelectorAll('.sidebar-item').forEach(el => {
                el.classList.remove('active');
            });

            // Show selected section
            document.getElementById(section + 'Section').style.display = 'block';

            // Add active class to clicked sidebar item
            element.classList.add('active');

            // Update header title (exactly like Flutter)
            const titles = {
                overview: 'Dashboard Overview',
                users: 'User Management',
                therapists: 'Therapist Management',
                appointments: 'Appointment Management',
                payments: 'Payment Management'
            };

            document.getElementById('headerTitle').textContent = titles[section];

            // Load section data
            switch(section) {
                case 'overview':
                    loadDashboardData();
                    break;
                case 'users':
                    loadUsers();
                    break;
                case 'therapists':
                    loadTherapists();
                    break;
                case 'appointments':
                    loadAppointments();
                    break;
                case 'payments':
                    loadPayments();
                    break;
            }
        }

        // Load dashboard statistics (exactly like Flutter)
        async function loadDashboardData() {
            try {
                // Fetch all data in parallel (exactly like Flutter)
                const [usersRes, therapistsRes, appointmentsRes] = await Promise.all([
                    fetch(API_BASE_URL + '/users'),
                    fetch(API_BASE_URL + '/therapists'),
                    fetch(API_BASE_URL + '/appointments')
                ]);

                const users = await usersRes.json();
                const therapists = await therapistsRes.json();
                const appointments = await appointmentsRes.json();

                // Calculate today's appointments (exactly like Flutter)
                const today = new Date();
                const todayAppointments = appointments.filter(appointment => {
                    if (appointment.appointmentDate) {
                        const appointmentDate = new Date(appointment.appointmentDate);
                        return appointmentDate.getFullYear() === today.getFullYear() &&
                               appointmentDate.getMonth() === today.getMonth() &&
                               appointmentDate.getDate() === today.getDate();
                    }
                    return false;
                }).length;

                // Calculate total revenue (exactly like Flutter)
                let totalRevenue = 0.0;
                appointments.forEach(appointment => {
                    if (appointment.fee) {
                        totalRevenue += parseFloat(appointment.fee);
                    }
                });

                // Update statistics (exactly like Flutter)
                document.getElementById('totalUsers').textContent = users.length || 0;
                document.getElementById('activeTherapists').textContent = therapists.length || 0;
                document.getElementById('todayAppointments').textContent = todayAppointments;
                document.getElementById('totalRevenue').textContent = '$' + totalRevenue.toFixed(2);

                // Load recent activity
                loadRecentActivity(appointments.slice(0, 10));

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load recent activity (exactly like Flutter)
        function loadRecentActivity(recentAppointments) {
            const container = document.getElementById('recentActivity');

            if (recentAppointments.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">No recent activity</p>';
                return;
            }

            const table = `
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9ff;">
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Date</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">User</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Therapist</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Type</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${recentAppointments.map(apt => `
                            <tr>
                                <td style="padding: 12px; border-bottom: 1px solid #eee;">${new Date(apt.appointmentDate || apt.createdAt).toLocaleDateString()}</td>
                                <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.userEmail || 'N/A'}</td>
                                <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.therapistEmail || 'N/A'}</td>
                                <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.type || 'N/A'}</td>
                                <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                    <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; background: #d4edda; color: #155724;">
                                        ${apt.status || 'Pending'}
                                    </span>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = table;
        }

        // Load users (exactly like Flutter UserManagementScreen)
        async function loadUsers() {
            const container = document.getElementById('usersTable');

            try {
                const response = await fetch(API_BASE_URL + '/users');
                const users = await response.json();

                const table = `
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9ff;">
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Username</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Email</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Role</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Status</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Created</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${users.map(user => `
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${user.username || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${user.email}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; background: #e3f2fd; color: #1976d2;">
                                            ${user.role || 'user'}
                                        </span>
                                    </td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; background: ${user.isActive ? '#d4edda' : '#f8d7da'}; color: ${user.isActive ? '#155724' : '#721c24'};">
                                            ${user.isActive ? 'Active' : 'Inactive'}
                                        </span>
                                    </td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${new Date(user.createdAt).toLocaleDateString()}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <button onclick="editUser('${user._id}')" style="padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; margin-right: 5px; cursor: pointer;">Edit</button>
                                        <button onclick="deleteUser('${user._id}')" style="padding: 6px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">Delete</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                container.innerHTML = table;
            } catch (error) {
                container.innerHTML = '<p style="text-align: center; color: #dc3545; padding: 20px;">Error loading users</p>';
                console.error('Error loading users:', error);
            }
        }

        // Load therapists (exactly like Flutter TherapistManagementScreen)
        async function loadTherapists() {
            const container = document.getElementById('therapistsTable');

            try {
                const response = await fetch(API_BASE_URL + '/therapists');
                const therapists = await response.json();

                const table = `
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9ff;">
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Name</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Email</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Specialization</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Status</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${therapists.map(therapist => `
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${therapist.username || therapist.name || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${therapist.email}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${therapist.specialization || 'General'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; background: #d4edda; color: #155724;">
                                            Active
                                        </span>
                                    </td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <button onclick="editTherapist('${therapist._id}')" style="padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; margin-right: 5px; cursor: pointer;">Edit</button>
                                        <button onclick="deleteTherapist('${therapist._id}')" style="padding: 6px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">Delete</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                container.innerHTML = table;
            } catch (error) {
                container.innerHTML = '<p style="text-align: center; color: #dc3545; padding: 20px;">Error loading therapists</p>';
                console.error('Error loading therapists:', error);
            }
        }

        // Load appointments (exactly like Flutter AppointmentManagementScreen)
        async function loadAppointments() {
            const container = document.getElementById('appointmentsTable');

            try {
                const response = await fetch(API_BASE_URL + '/appointments');
                const appointments = await response.json();

                const table = `
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9ff;">
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">User</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Therapist</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Date</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Time</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Type</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Status</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${appointments.map(apt => `
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.userEmail || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.therapistEmail || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.appointmentDate ? new Date(apt.appointmentDate).toLocaleDateString() : 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.appointmentTime || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.type || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; background: #fff3cd; color: #856404;">
                                            ${apt.status || 'Pending'}
                                        </span>
                                    </td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <button onclick="editAppointment('${apt._id}')" style="padding: 6px 12px; background: #007bff; color: white; border: none; border-radius: 4px; margin-right: 5px; cursor: pointer;">Edit</button>
                                        <button onclick="cancelAppointment('${apt._id}')" style="padding: 6px 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                container.innerHTML = table;
            } catch (error) {
                container.innerHTML = '<p style="text-align: center; color: #dc3545; padding: 20px;">Error loading appointments</p>';
                console.error('Error loading appointments:', error);
            }
        }

        // Load payments (exactly like Flutter PaymentManagementScreen)
        async function loadPayments() {
            const container = document.getElementById('paymentsTable');

            // For now, show a placeholder since payments might not be fully implemented
            container.innerHTML = `
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9ff;">
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Payment ID</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">User</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Amount</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Date</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Status</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                                Payment management coming soon...
                            </td>
                        </tr>
                    </tbody>
                </table>
            `;
        }

        // Action functions (exactly like Flutter)
        function editUser(userId) {
            alert('Edit user: ' + userId);
        }

        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user?')) {
                alert('Delete user: ' + userId);
            }
        }

        function editTherapist(therapistId) {
            alert('Edit therapist: ' + therapistId);
        }

        function deleteTherapist(therapistId) {
            if (confirm('Are you sure you want to delete this therapist?')) {
                alert('Delete therapist: ' + therapistId);
            }
        }

        function editAppointment(appointmentId) {
            alert('Edit appointment: ' + appointmentId);
        }

        function cancelAppointment(appointmentId) {
            if (confirm('Are you sure you want to cancel this appointment?')) {
                alert('Cancel appointment: ' + appointmentId);
            }
        }

        function showNotifications() {
            alert('Notifications feature coming soon!');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                document.getElementById('adminDashboard').style.display = 'none';
                document.getElementById('loginScreen').style.display = 'flex';

                // Reset form
                document.getElementById('loginForm').reset();
                document.getElementById('email').value = '<EMAIL>';
                document.getElementById('password').value = 'MindEase@Admin2024';
            }
        }

        // Auto-load dashboard on page load if needed
        window.addEventListener('load', () => {
            // You can add auto-login logic here if needed
        });
    </script>
</body>
</html>
