<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindEase Admin Portal</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #6B73FF 100%); min-height: 100vh; }
        
        .login-container { display: flex; justify-content: center; align-items: center; min-height: 100vh; padding: 24px; }
        .login-card { background: white; padding: 40px; border-radius: 24px; box-shadow: 0 15px 30px rgba(0,0,0,0.15); width: 100%; max-width: 450px; }
        .admin-icon { width: 100px; height: 100px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 28px; display: flex; align-items: center; justify-content: center; margin: 0 auto 32px; }
        .admin-icon i { font-size: 50px; color: white; }
        .admin-title { font-size: 32px; font-weight: bold; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; text-align: center; margin-bottom: 8px; }
        .admin-subtitle { color: #666; font-size: 16px; text-align: center; margin-bottom: 40px; }
        .form-group { margin-bottom: 24px; }
        .form-group label { display: block; margin-bottom: 12px; color: #1A237E; font-weight: 600; font-size: 16px; }
        .form-group input { width: 100%; padding: 16px 20px; border: 2px solid #E8EAF6; border-radius: 16px; font-size: 16px; transition: all 0.3s ease; background: #FAFAFA; }
        .form-group input:focus { outline: none; border-color: #667eea; background: white; }
        .login-btn { width: 100%; padding: 18px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 16px; font-size: 18px; font-weight: 600; cursor: pointer; }
        .error-message { background: #FFEBEE; color: #C62828; padding: 16px; border-radius: 12px; margin-bottom: 20px; display: none; }
        
        .admin-dashboard { display: none; min-height: 100vh; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); }
        .dashboard-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px 24px; display: flex; justify-content: space-between; align-items: center; }
        .header-title { font-size: 20px; font-weight: bold; }
        .header-btn { background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 12px; border-radius: 12px; cursor: pointer; }
        .dashboard-content { display: flex; min-height: calc(100vh - 72px); }
        
        .sidebar { width: 280px; background: white; }
        .sidebar-header { background: linear-gradient(135deg, #1A237E 0%, #3949AB 100%); color: white; padding: 24px; }
        .sidebar-title { font-size: 24px; font-weight: bold; margin-bottom: 4px; }
        .sidebar-subtitle { font-size: 14px; color: rgba(255,255,255,0.7); }
        .sidebar-item { display: flex; align-items: center; padding: 16px 24px; cursor: pointer; border: none; background: none; width: 100%; text-align: left; }
        .sidebar-item:hover { background: #f8f9ff; }
        .sidebar-item.active { background: rgba(26, 35, 126, 0.1); border-right: 4px solid #1A237E; }
        .sidebar-item i { margin-right: 16px; width: 20px; color: #666; }
        .sidebar-item.active i { color: #1A237E; }
        .sidebar-item span { color: #333; font-weight: 500; }
        .sidebar-item.active span { color: #1A237E; font-weight: bold; }
        
        .main-content { flex: 1; padding: 16px; }
        .stats-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 16px; margin-bottom: 24px; }
        .stat-card { background: white; padding: 20px; border-radius: 20px; box-shadow: 0 4px 10px rgba(0,0,0,0.1); }
        .stat-number { font-size: 28px; font-weight: bold; color: #4A90E2; margin-bottom: 8px; }
        .stat-label { color: #666; font-size: 16px; font-weight: 500; }
        .content-section { background: white; border-radius: 16px; padding: 24px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .section-title { font-size: 24px; font-weight: bold; color: #1A237E; margin-bottom: 16px; }
        table { width: 100%; border-collapse: collapse; margin-top: 16px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9ff; font-weight: bold; }
        .btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; margin-right: 5px; color: white; }
        .btn-primary { background: #007bff; }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="admin-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            <div class="admin-title">MindEase Admin</div>
            <div class="admin-subtitle">Administrative Panel</div>
            <div id="errorMessage" class="error-message"></div>
            <form onsubmit="return false;">
                <div class="form-group">
                    <label>Admin Email</label>
                    <input type="email" id="email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label>Password</label>
                    <input type="password" id="password" value="MindEase@Admin2024">
                </div>
                <button type="button" class="login-btn" onclick="login()">
                    <i class="fas fa-sign-in-alt"></i> Login to Dashboard
                </button>
            </form>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="adminDashboard" class="admin-dashboard">
        <div class="dashboard-header">
            <div class="header-title" id="headerTitle">Dashboard Overview</div>
            <div>
                <button class="header-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
        <div class="dashboard-content">
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-title">MindEase Admin</div>
                    <div class="sidebar-subtitle">Administrative Panel</div>
                </div>
                <div>
                    <button class="sidebar-item active" onclick="showSection('overview', this)">
                        <i class="fas fa-chart-line"></i>
                        <span>Dashboard</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('users', this)">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('therapists', this)">
                        <i class="fas fa-user-md"></i>
                        <span>Therapists</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('appointments', this)">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Appointments</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('payments', this)">
                        <i class="fas fa-credit-card"></i>
                        <span>Payments</span>
                    </button>
                </div>
            </div>
            <div class="main-content">
                <!-- Overview Section -->
                <div id="overviewSection" class="content-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number" id="totalUsers">0</div>
                            <div class="stat-label">Total Users</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="activeTherapists">0</div>
                            <div class="stat-label">Active Therapists</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="todayAppointments">0</div>
                            <div class="stat-label">Today's Appointments</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="totalRevenue">$0</div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                    </div>
                </div>
                <!-- Users Section -->
                <div id="usersSection" class="content-section" style="display: none;">
                    <div class="section-title">User Management</div>
                    <div id="usersTable">Loading users...</div>
                </div>
                <!-- Therapists Section -->
                <div id="therapistsSection" class="content-section" style="display: none;">
                    <div class="section-title">Therapist Management</div>
                    <div id="therapistsTable">Loading therapists...</div>
                </div>
                <!-- Appointments Section -->
                <div id="appointmentsSection" class="content-section" style="display: none;">
                    <div class="section-title">Appointment Management</div>
                    <div id="appointmentsTable">Loading appointments...</div>
                </div>
                <!-- Payments Section -->
                <div id="paymentsSection" class="content-section" style="display: none;">
                    <div class="section-title">Payment Management</div>
                    <div id="paymentsTable">Payment management coming soon...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('Admin portal script loaded');

        function login() {
            console.log('Login function called');
            
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            
            console.log('Email:', email);
            console.log('Password:', password);
            
            if (email === '<EMAIL>' && password === 'MindEase@Admin2024') {
                console.log('Login successful');
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('adminDashboard').style.display = 'block';
                loadDashboardData();
            } else {
                console.log('Login failed');
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.textContent = 'Invalid admin credentials';
                errorDiv.style.display = 'block';
            }
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                document.getElementById('adminDashboard').style.display = 'none';
                document.getElementById('loginScreen').style.display = 'flex';
                document.getElementById('errorMessage').style.display = 'none';
            }
        }

        function showSection(section, element) {
            document.querySelectorAll('.content-section').forEach(el => el.style.display = 'none');
            document.querySelectorAll('.sidebar-item').forEach(el => el.classList.remove('active'));
            
            document.getElementById(section + 'Section').style.display = 'block';
            element.classList.add('active');
            
            const titles = {
                overview: 'Dashboard Overview',
                users: 'User Management',
                therapists: 'Therapist Management',
                appointments: 'Appointment Management',
                payments: 'Payment Management'
            };
            document.getElementById('headerTitle').textContent = titles[section];
            
            if (section === 'users') loadUsers();
            else if (section === 'therapists') loadTherapists();
            else if (section === 'appointments') loadAppointments();
        }

        async function loadDashboardData() {
            console.log('Loading dashboard data');
            try {
                const [usersRes, therapistsRes, appointmentsRes] = await Promise.all([
                    fetch('/api/users'),
                    fetch('/api/therapists'),
                    fetch('/api/appointments')
                ]);
                
                const users = await usersRes.json();
                const therapists = await therapistsRes.json();
                const appointments = await appointmentsRes.json();
                
                const today = new Date();
                const todayAppointments = appointments.filter(apt => {
                    if (apt.appointmentDate) {
                        const aptDate = new Date(apt.appointmentDate);
                        return aptDate.toDateString() === today.toDateString();
                    }
                    return false;
                }).length;
                
                let totalRevenue = 0;
                appointments.forEach(apt => {
                    if (apt.fee) totalRevenue += parseFloat(apt.fee);
                });
                
                document.getElementById('totalUsers').textContent = users.length;
                document.getElementById('activeTherapists').textContent = therapists.length;
                document.getElementById('todayAppointments').textContent = todayAppointments;
                document.getElementById('totalRevenue').textContent = '$' + totalRevenue.toFixed(2);
                
                console.log('Dashboard data loaded successfully');
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        async function loadUsers() {
            console.log('Loading users');
            try {
                const response = await fetch('/api/users');
                const users = await response.json();
                
                let html = '<table><tr><th>Username</th><th>Email</th><th>Role</th><th>Status</th><th>Created</th><th>Actions</th></tr>';
                users.forEach(user => {
                    html += `<tr>
                        <td>${user.username || 'N/A'}</td>
                        <td>${user.email}</td>
                        <td>${user.role || 'user'}</td>
                        <td><span style="color: ${user.isBlocked ? 'red' : 'green'}">${user.isBlocked ? 'Blocked' : 'Active'}</span></td>
                        <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                        <td>
                            <button class="btn ${user.isBlocked ? 'btn-success' : 'btn-danger'}" onclick="toggleUserBlock('${user._id}', ${user.isBlocked})">
                                ${user.isBlocked ? 'Unblock' : 'Block'}
                            </button>
                        </td>
                    </tr>`;
                });
                html += '</table>';
                document.getElementById('usersTable').innerHTML = html;
            } catch (error) {
                document.getElementById('usersTable').innerHTML = 'Error loading users';
                console.error('Error loading users:', error);
            }
        }

        async function loadTherapists() {
            console.log('Loading therapists');
            try {
                const response = await fetch('/api/therapists');
                const therapists = await response.json();
                
                let html = '<table><tr><th>Name</th><th>Email</th><th>Specialization</th><th>Actions</th></tr>';
                therapists.forEach(therapist => {
                    html += `<tr>
                        <td>${therapist.username || therapist.name || 'N/A'}</td>
                        <td>${therapist.email}</td>
                        <td>${therapist.specialization || 'General'}</td>
                        <td>
                            <button class="btn btn-primary" onclick="alert('Edit therapist: ${therapist._id}')">Edit</button>
                            <button class="btn btn-danger" onclick="if(confirm('Delete therapist?')) alert('Delete: ${therapist._id}')">Delete</button>
                        </td>
                    </tr>`;
                });
                html += '</table>';
                document.getElementById('therapistsTable').innerHTML = html;
            } catch (error) {
                document.getElementById('therapistsTable').innerHTML = 'Error loading therapists';
                console.error('Error loading therapists:', error);
            }
        }

        async function loadAppointments() {
            console.log('Loading appointments');
            try {
                const response = await fetch('/api/appointments');
                const appointments = await response.json();
                
                let html = '<table><tr><th>User</th><th>Therapist</th><th>Date</th><th>Status</th><th>Fee</th><th>Actions</th></tr>';
                appointments.forEach(apt => {
                    html += `<tr>
                        <td>${apt.userEmail || 'N/A'}</td>
                        <td>${apt.therapistEmail || 'N/A'}</td>
                        <td>${apt.appointmentDate ? new Date(apt.appointmentDate).toLocaleDateString() : 'N/A'}</td>
                        <td>${apt.status || 'Pending'}</td>
                        <td>$${apt.fee || '0.00'}</td>
                        <td>
                            <button class="btn btn-primary" onclick="alert('Edit appointment: ${apt._id}')">Edit</button>
                            <button class="btn btn-danger" onclick="if(confirm('Cancel appointment?')) alert('Cancel: ${apt._id}')">Cancel</button>
                        </td>
                    </tr>`;
                });
                html += '</table>';
                document.getElementById('appointmentsTable').innerHTML = html;
            } catch (error) {
                document.getElementById('appointmentsTable').innerHTML = 'Error loading appointments';
                console.error('Error loading appointments:', error);
            }
        }

        async function toggleUserBlock(userId, isCurrentlyBlocked) {
            if (!confirm(`Are you sure you want to ${isCurrentlyBlocked ? 'unblock' : 'block'} this user?`)) return;
            
            try {
                const response = await fetch(`/api/users/${userId}/block`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ isBlocked: !isCurrentlyBlocked })
                });
                
                if (response.ok) {
                    alert(`User ${isCurrentlyBlocked ? 'unblocked' : 'blocked'} successfully!`);
                    loadUsers();
                } else {
                    throw new Error('Failed to update user status');
                }
            } catch (error) {
                alert('Error updating user status: ' + error.message);
            }
        }

        console.log('All functions loaded, admin portal ready');
    </script>
</body>
</html>
