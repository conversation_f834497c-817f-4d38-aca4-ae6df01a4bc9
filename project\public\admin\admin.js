// Admin Portal JavaScript
const API_BASE_URL = 'http://***********:3000/api';

// Login functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, setting up login form...');

    const loginForm = document.getElementById('loginForm');
    if (!loginForm) {
        console.error('Login form not found!');
        return;
    }

    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        console.log('Login form submitted');

        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const loginBtn = document.getElementById('loginBtn');
        const errorMessage = document.getElementById('errorMessage');

        console.log('Email:', email);
        console.log('Password length:', password.length);

        // Show loading state
        loginBtn.disabled = true;
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';
        errorMessage.style.display = 'none';

        // Simple client-side authentication (matching Flutter app)
        const adminEmail = "<EMAIL>";
        const adminPassword = "MindEase@Admin2024";

        try {
            console.log('Checking credentials...');
            if (email === adminEmail && password === adminPassword) {
                console.log('Credentials match! Logging in...');

                // Store admin token
                localStorage.setItem('adminToken', 'admin-token-' + Date.now());
                localStorage.setItem('adminUser', JSON.stringify({
                    email: adminEmail,
                    role: 'admin'
                }));

                // Show dashboard
                document.getElementById('loginScreen').style.display = 'none';
                document.getElementById('adminDashboard').style.display = 'block';

                console.log('Dashboard should be visible now');

                // Load dashboard data
                loadDashboardData();
            } else {
                console.log('Credentials do not match');
                console.log('Expected email:', adminEmail);
                console.log('Received email:', email);
                console.log('Expected password:', adminPassword);
                console.log('Received password:', password);
                throw new Error('Invalid admin credentials');
            }
        } catch (error) {
            console.error('Login error:', error);
            errorMessage.textContent = error.message;
            errorMessage.style.display = 'block';
        } finally {
            loginBtn.disabled = false;
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Login to Dashboard';
        }
    });
});

// Debug login function
function debugLogin() {
    console.log('Debug login clicked');

    // Store admin token
    localStorage.setItem('adminToken', 'admin-token-' + Date.now());
    localStorage.setItem('adminUser', JSON.stringify({
        email: '<EMAIL>',
        role: 'admin'
    }));

    // Show dashboard
    document.getElementById('loginScreen').style.display = 'none';
    document.getElementById('adminDashboard').style.display = 'block';

    console.log('Dashboard should be visible now');

    // Load dashboard data
    loadDashboardData();
}

// Toggle password visibility
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.querySelector('.password-toggle i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Show different sections
function showSection(section) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(el => {
        el.style.display = 'none';
    });
    
    // Remove active class from all sidebar items
    document.querySelectorAll('.sidebar-item').forEach(el => {
        el.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(`${section}Section`).style.display = 'block';
    
    // Add active class to clicked sidebar item
    event.target.classList.add('active');
    
    // Update header title
    const titles = {
        overview: 'Dashboard Overview',
        users: 'User Management',
        therapists: 'Therapist Management',
        appointments: 'Appointment Management',
        payments: 'Payment Management'
    };
    
    document.getElementById('headerTitle').textContent = titles[section];
    
    // Load section data
    switch(section) {
        case 'overview':
            loadDashboardData();
            break;
        case 'users':
            loadUsers();
            break;
        case 'therapists':
            loadTherapists();
            break;
        case 'appointments':
            loadAppointments();
            break;
        case 'payments':
            loadPayments();
            break;
    }
}

// Load dashboard statistics
async function loadDashboardData() {
    try {
        // Fetch all data in parallel
        const [usersRes, therapistsRes, appointmentsRes] = await Promise.all([
            fetch(`${API_BASE_URL}/users`),
            fetch(`${API_BASE_URL}/therapists`),
            fetch(`${API_BASE_URL}/appointments`)
        ]);
        
        const users = await usersRes.json();
        const therapists = await therapistsRes.json();
        const appointments = await appointmentsRes.json();
        
        // Update statistics
        document.getElementById('totalUsers').textContent = users.length || 0;
        document.getElementById('activeTherapists').textContent = therapists.length || 0;
        
        // Calculate today's appointments
        const today = new Date().toDateString();
        const todayAppointments = appointments.filter(apt => {
            if (apt.appointmentDate) {
                return new Date(apt.appointmentDate).toDateString() === today;
            }
            return false;
        }).length;
        
        document.getElementById('todayAppointments').textContent = todayAppointments;
        
        // Calculate total revenue
        const totalRevenue = appointments.reduce((sum, apt) => {
            return sum + (apt.fee || 0);
        }, 0);
        
        document.getElementById('totalRevenue').textContent = `$${totalRevenue.toFixed(2)}`;
        
        // Load recent activity
        loadRecentActivity(appointments.slice(0, 10));
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

// Load recent activity
function loadRecentActivity(recentAppointments) {
    const container = document.getElementById('recentActivity');
    
    if (recentAppointments.length === 0) {
        container.innerHTML = '<p>No recent activity</p>';
        return;
    }
    
    const table = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>User</th>
                    <th>Therapist</th>
                    <th>Type</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                ${recentAppointments.map(apt => `
                    <tr>
                        <td>${new Date(apt.appointmentDate || apt.createdAt).toLocaleDateString()}</td>
                        <td>${apt.userEmail || 'N/A'}</td>
                        <td>${apt.therapistEmail || 'N/A'}</td>
                        <td>${apt.type || 'N/A'}</td>
                        <td><span class="status-badge status-${apt.status || 'pending'}">${apt.status || 'Pending'}</span></td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
    
    container.innerHTML = table;
}

// Load users
async function loadUsers() {
    const container = document.getElementById('usersTable');
    
    try {
        const response = await fetch(`${API_BASE_URL}/users`);
        const users = await response.json();
        
        const table = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${users.map(user => `
                        <tr>
                            <td>${user._id}</td>
                            <td>${user.username || 'N/A'}</td>
                            <td>${user.email}</td>
                            <td><span class="status-badge status-active">${user.role || 'user'}</span></td>
                            <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                            <td>
                                <button class="action-btn btn-edit" onclick="editUser('${user._id}')">Edit</button>
                                <button class="action-btn btn-delete" onclick="deleteUser('${user._id}')">Delete</button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        
        container.innerHTML = table;
    } catch (error) {
        container.innerHTML = '<p>Error loading users</p>';
        console.error('Error loading users:', error);
    }
}

// Load therapists
async function loadTherapists() {
    const container = document.getElementById('therapistsTable');
    
    try {
        const response = await fetch(`${API_BASE_URL}/therapists`);
        const therapists = await response.json();
        
        const table = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Specialization</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${therapists.map(therapist => `
                        <tr>
                            <td>${therapist._id}</td>
                            <td>${therapist.username || therapist.name || 'N/A'}</td>
                            <td>${therapist.email}</td>
                            <td>${therapist.specialization || 'General'}</td>
                            <td><span class="status-badge status-active">Active</span></td>
                            <td>
                                <button class="action-btn btn-edit" onclick="editTherapist('${therapist._id}')">Edit</button>
                                <button class="action-btn btn-delete" onclick="deleteTherapist('${therapist._id}')">Delete</button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        
        container.innerHTML = table;
    } catch (error) {
        container.innerHTML = '<p>Error loading therapists</p>';
        console.error('Error loading therapists:', error);
    }
}

// Load appointments
async function loadAppointments() {
    const container = document.getElementById('appointmentsTable');
    
    try {
        const response = await fetch(`${API_BASE_URL}/appointments`);
        const appointments = await response.json();
        
        const table = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>User</th>
                        <th>Therapist</th>
                        <th>Date</th>
                        <th>Time</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${appointments.map(apt => `
                        <tr>
                            <td>${apt._id}</td>
                            <td>${apt.userEmail || 'N/A'}</td>
                            <td>${apt.therapistEmail || 'N/A'}</td>
                            <td>${apt.appointmentDate ? new Date(apt.appointmentDate).toLocaleDateString() : 'N/A'}</td>
                            <td>${apt.appointmentTime || 'N/A'}</td>
                            <td>${apt.type || 'N/A'}</td>
                            <td><span class="status-badge status-${apt.status || 'pending'}">${apt.status || 'Pending'}</span></td>
                            <td>
                                <button class="action-btn btn-edit" onclick="editAppointment('${apt._id}')">Edit</button>
                                <button class="action-btn btn-delete" onclick="deleteAppointment('${apt._id}')">Cancel</button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        
        container.innerHTML = table;
    } catch (error) {
        container.innerHTML = '<p>Error loading appointments</p>';
        console.error('Error loading appointments:', error);
    }
}

// Load payments
async function loadPayments() {
    const container = document.getElementById('paymentsTable');
    
    // For now, show a placeholder since payments might not be fully implemented
    container.innerHTML = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>Payment ID</th>
                    <th>User</th>
                    <th>Amount</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                        Payment management coming soon...
                    </td>
                </tr>
            </tbody>
        </table>
    `;
}

// Action functions
function editUser(userId) {
    alert(`Edit user: ${userId}`);
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
        // Implement delete functionality
        alert(`Delete user: ${userId}`);
    }
}

function editTherapist(therapistId) {
    alert(`Edit therapist: ${therapistId}`);
}

function deleteTherapist(therapistId) {
    if (confirm('Are you sure you want to delete this therapist?')) {
        alert(`Delete therapist: ${therapistId}`);
    }
}

function editAppointment(appointmentId) {
    alert(`Edit appointment: ${appointmentId}`);
}

function deleteAppointment(appointmentId) {
    if (confirm('Are you sure you want to cancel this appointment?')) {
        alert(`Cancel appointment: ${appointmentId}`);
    }
}

function showNotifications() {
    alert('Notifications feature coming soon!');
}

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminUser');
        
        document.getElementById('adminDashboard').style.display = 'none';
        document.getElementById('loginScreen').style.display = 'flex';
        
        // Reset form
        document.getElementById('loginForm').reset();
    }
}

// Check if admin is already logged in
window.addEventListener('load', () => {
    const adminToken = localStorage.getItem('adminToken');
    if (adminToken) {
        document.getElementById('loginScreen').style.display = 'none';
        document.getElementById('adminDashboard').style.display = 'block';
        loadDashboardData();
    }
});
