## 1.0.11

* Updates to pigeon 21.
* Updates minimum supported SDK version to Flutter 3.16/Dart 3.2.

## 1.0.10

* Adds pub topics to package metadata.
* Updates minimum supported SDK version to Flutter 3.7/Dart 2.19.

## 1.0.9

* Updates to Pigeon 10.0.
* Fixes stale ignore: prefer_const_constructors.
* Updates minimum supported SDK version to Flutter 3.10/Dart 3.0.

## 1.0.8

* Sets a cmake_policy compatibility version to fix build warnings.

## 1.0.7

* Clarifies explanation of endorsement in README.
* Aligns Dart and Flutter SDK constraints.

## 1.0.6

* Updates links for the merge of flutter/plugins into flutter/packages.
* Updates minimum Flutter version to 3.0.

## 1.0.5

* Switches internal implementation to Pigeon.

## 1.0.4

* Updates imports for `prefer_relative_imports`.
* Updates minimum Flutter version to 2.10.

## 1.0.3

* Fixes avoid_redundant_argument_values lint warnings and minor typos.

## 1.0.2

* Updates `local_auth_platform_interface` constraint to the correct minimum
  version.

## 1.0.1

* Updates references to the obsolete master branch.

## 1.0.0

* Initial release of Windows support.
