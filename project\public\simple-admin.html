<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindEase Admin Portal</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .stat-label {
            font-size: 16px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🧠 MindEase Admin Portal</div>
            <p>Welcome to the admin dashboard</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">0</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTherapists">0</div>
                <div class="stat-label">Total Therapists</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalAppointments">0</div>
                <div class="stat-label">Total Appointments</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalRevenue">$0</div>
                <div class="stat-label">Total Revenue</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Recent Users</div>
            <div id="usersTable" class="loading">Loading users...</div>
        </div>

        <div class="section">
            <div class="section-title">Recent Therapists</div>
            <div id="therapistsTable" class="loading">Loading therapists...</div>
        </div>

        <div class="section">
            <div class="section-title">Recent Appointments</div>
            <div id="appointmentsTable" class="loading">Loading appointments...</div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';

        // Load all data when page loads
        window.onload = function() {
            loadUsers();
            loadTherapists();
            loadAppointments();
        };

        async function loadUsers() {
            try {
                const response = await fetch(`${API_BASE}/users`);
                const users = await response.json();
                
                document.getElementById('totalUsers').textContent = users.length;
                
                const table = `
                    <table>
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Email</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${users.slice(0, 10).map(user => `
                                <tr>
                                    <td>${user.username || 'N/A'}</td>
                                    <td>${user.email}</td>
                                    <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                                    <td>
                                        <button class="btn btn-primary" onclick="editUser('${user._id}')">Edit</button>
                                        <button class="btn btn-danger" onclick="deleteUser('${user._id}')">Delete</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
                
                document.getElementById('usersTable').innerHTML = table;
            } catch (error) {
                document.getElementById('usersTable').innerHTML = '<p>Error loading users</p>';
            }
        }

        async function loadTherapists() {
            try {
                const response = await fetch(`${API_BASE}/therapists`);
                const therapists = await response.json();
                
                document.getElementById('totalTherapists').textContent = therapists.length;
                
                const table = `
                    <table>
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Specialization</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${therapists.slice(0, 10).map(therapist => `
                                <tr>
                                    <td>${therapist.username || therapist.name || 'N/A'}</td>
                                    <td>${therapist.email}</td>
                                    <td>${therapist.specialization || 'General'}</td>
                                    <td>
                                        <button class="btn btn-primary" onclick="editTherapist('${therapist._id}')">Edit</button>
                                        <button class="btn btn-danger" onclick="deleteTherapist('${therapist._id}')">Delete</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
                
                document.getElementById('therapistsTable').innerHTML = table;
            } catch (error) {
                document.getElementById('therapistsTable').innerHTML = '<p>Error loading therapists</p>';
            }
        }

        async function loadAppointments() {
            try {
                const response = await fetch(`${API_BASE}/appointments`);
                const appointments = await response.json();
                
                document.getElementById('totalAppointments').textContent = appointments.length;
                
                // Calculate revenue
                const revenue = appointments.reduce((sum, apt) => sum + (apt.fee || 0), 0);
                document.getElementById('totalRevenue').textContent = `$${revenue.toFixed(2)}`;
                
                const table = `
                    <table>
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Therapist</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${appointments.slice(0, 10).map(apt => `
                                <tr>
                                    <td>${apt.userEmail || 'N/A'}</td>
                                    <td>${apt.therapistEmail || 'N/A'}</td>
                                    <td>${apt.appointmentDate ? new Date(apt.appointmentDate).toLocaleDateString() : 'N/A'}</td>
                                    <td>${apt.status || 'Pending'}</td>
                                    <td>
                                        <button class="btn btn-primary" onclick="editAppointment('${apt._id}')">Edit</button>
                                        <button class="btn btn-danger" onclick="cancelAppointment('${apt._id}')">Cancel</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
                
                document.getElementById('appointmentsTable').innerHTML = table;
            } catch (error) {
                document.getElementById('appointmentsTable').innerHTML = '<p>Error loading appointments</p>';
            }
        }

        function editUser(id) {
            alert(`Edit user: ${id}`);
        }

        function deleteUser(id) {
            if (confirm('Delete this user?')) {
                alert(`Delete user: ${id}`);
            }
        }

        function editTherapist(id) {
            alert(`Edit therapist: ${id}`);
        }

        function deleteTherapist(id) {
            if (confirm('Delete this therapist?')) {
                alert(`Delete therapist: ${id}`);
            }
        }

        function editAppointment(id) {
            alert(`Edit appointment: ${id}`);
        }

        function cancelAppointment(id) {
            if (confirm('Cancel this appointment?')) {
                alert(`Cancel appointment: ${id}`);
            }
        }
    </script>
</body>
</html>
