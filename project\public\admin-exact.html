<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindEase Admin Portal</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #6B73FF 100%);
            min-height: 100vh;
        }

        /* Login Screen - Exact Flutter Match */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 24px;
        }

        .login-card {
            background: white;
            padding: 40px;
            border-radius: 24px;
            box-shadow: 0 15px 30px rgba(0,0,0,0.15), 0 -5px 10px rgba(255,255,255,0.1);
            width: 100%;
            max-width: 450px;
        }

        .admin-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 32px;
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }

        .admin-icon i {
            font-size: 50px;
            color: white;
        }

        .admin-title {
            font-size: 32px;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-align: center;
            margin-bottom: 8px;
        }

        .admin-subtitle {
            color: #666;
            font-size: 16px;
            text-align: center;
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            margin-bottom: 12px;
            color: #1A237E;
            font-weight: 600;
            font-size: 16px;
        }

        .form-group input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #E8EAF6;
            border-radius: 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #FAFAFA;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .password-container {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            font-size: 18px;
        }

        .login-btn {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #FFEBEE;
            color: #C62828;
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 20px;
            border-left: 4px solid #C62828;
            font-size: 14px;
        }

        /* Dashboard - Exact Flutter Match */
        .admin-dashboard {
            display: none;
            min-height: 100vh;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 24px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
        }

        .header-actions {
            display: flex;
            gap: 8px;
        }

        .header-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .header-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .dashboard-content {
            display: flex;
            min-height: calc(100vh - 72px);
        }

        /* Sidebar - Exact Flutter Match */
        .sidebar {
            width: 280px;
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            background: linear-gradient(135deg, #1A237E 0%, #3949AB 100%);
            color: white;
            padding: 24px;
        }

        .sidebar-icon {
            width: 56px;
            height: 56px;
            background: rgba(255,255,255,0.2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
        }

        .sidebar-icon i {
            font-size: 32px;
            color: white;
        }

        .sidebar-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .sidebar-subtitle {
            font-size: 14px;
            color: rgba(255,255,255,0.7);
        }

        .sidebar-menu {
            padding: 0;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
        }

        .sidebar-item:hover {
            background: #f8f9ff;
        }

        .sidebar-item.active {
            background: rgba(26, 35, 126, 0.1);
            border-right: 4px solid #1A237E;
        }

        .sidebar-item i {
            margin-right: 16px;
            width: 20px;
            color: #666;
        }

        .sidebar-item.active i {
            color: #1A237E;
        }

        .sidebar-item span {
            color: #333;
            font-weight: 500;
        }

        .sidebar-item.active span {
            color: #1A237E;
            font-weight: bold;
        }

        .main-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }

        /* Stats Cards - Exact Flutter Match */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 20px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .stat-card.users {
            background: linear-gradient(135deg, rgba(74, 144, 226, 0.1) 0%, rgba(74, 144, 226, 0.05) 100%);
            border: 1px solid rgba(74, 144, 226, 0.2);
        }

        .stat-card.therapists {
            background: linear-gradient(135deg, rgba(80, 200, 120, 0.1) 0%, rgba(80, 200, 120, 0.05) 100%);
            border: 1px solid rgba(80, 200, 120, 0.2);
        }

        .stat-card.appointments {
            background: linear-gradient(135deg, rgba(255, 140, 66, 0.1) 0%, rgba(255, 140, 66, 0.05) 100%);
            border: 1px solid rgba(255, 140, 66, 0.2);
        }

        .stat-card.revenue {
            background: linear-gradient(135deg, rgba(155, 89, 182, 0.1) 0%, rgba(155, 89, 182, 0.05) 100%);
            border: 1px solid rgba(155, 89, 182, 0.2);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stat-icon.users {
            background: rgba(74, 144, 226, 0.2);
            color: #4A90E2;
        }

        .stat-icon.therapists {
            background: rgba(80, 200, 120, 0.2);
            color: #50C878;
        }

        .stat-icon.appointments {
            background: rgba(255, 140, 66, 0.2);
            color: #FF8C42;
        }

        .stat-icon.revenue {
            background: rgba(155, 89, 182, 0.2);
            color: #9B59B6;
        }

        .stat-number {
            font-size: 28px;
            font-weight: bold;
        }

        .stat-number.users { color: #4A90E2; }
        .stat-number.therapists { color: #50C878; }
        .stat-number.appointments { color: #FF8C42; }
        .stat-number.revenue { color: #9B59B6; }

        .stat-label {
            color: #666;
            font-size: 16px;
            font-weight: 500;
        }

        .section-title {
            font-size: 24px;
            font-weight: bold;
            color: #1A237E;
            margin-bottom: 16px;
        }

        .quick-actions {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
        }

        .quick-action-card {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quick-action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }

        .quick-action-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
        }

        .quick-action-icon.block {
            background: rgba(231, 76, 60, 0.2);
            color: #E74C3C;
        }

        .quick-action-icon.payment {
            background: rgba(39, 174, 96, 0.2);
            color: #27AE60;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 40px;
            color: #666;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }

        .login-card {
            animation: fadeIn 0.6s ease-out;
        }

        .sidebar {
            animation: slideIn 0.4s ease-out;
        }

        .stat-card {
            animation: fadeIn 0.8s ease-out;
            animation-fill-mode: both;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .stat-card:nth-child(4) { animation-delay: 0.4s; }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        /* Hover effects for interactive elements */
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        }

        .quick-action-card:hover {
            transform: translateY(-4px) scale(1.02);
        }

        .sidebar-item:hover {
            transform: translateX(4px);
        }

        /* Loading pulse effect */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .loading-pulse {
            animation: pulse 1.5s ease-in-out infinite;
        }

        .content-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        @media (max-width: 768px) {
            .dashboard-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="admin-icon">
                <i class="fas fa-user-shield"></i>
            </div>
            
            <div class="admin-title">MindEase Admin</div>
            <div class="admin-subtitle">Administrative Panel</div>

            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <form id="loginForm">
                <div class="form-group">
                    <label for="email">Admin Email</label>
                    <input type="email" id="email" required value="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="password-container">
                        <input type="password" id="password" required value="MindEase@Admin2024">
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i> Login to Dashboard
                </button>
            </form>
        </div>
    </div>

    <!-- Admin Dashboard -->
    <div id="adminDashboard" class="admin-dashboard">
        <div class="dashboard-header">
            <div class="header-title" id="headerTitle">Dashboard Overview</div>
            <div class="header-actions">
                <button class="header-btn" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                </button>
                <button class="header-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>

        <div class="dashboard-content">
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="sidebar-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="sidebar-title">MindEase Admin</div>
                    <div class="sidebar-subtitle">Administrative Panel</div>
                </div>
                
                <div class="sidebar-menu">
                    <button class="sidebar-item active" onclick="showSection('overview', this)">
                        <i class="fas fa-chart-line"></i>
                        <span>Dashboard</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('users', this)">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('therapists', this)">
                        <i class="fas fa-user-md"></i>
                        <span>Therapists</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('appointments', this)">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Appointments</span>
                    </button>
                    <button class="sidebar-item" onclick="showSection('payments', this)">
                        <i class="fas fa-credit-card"></i>
                        <span>Payments</span>
                    </button>
                </div>
            </div>

            <div class="main-content">
                <!-- Overview Section -->
                <div id="overviewSection" class="content-section">
                    <div class="stats-grid">
                        <div class="stat-card users">
                            <div class="stat-header">
                                <div class="stat-icon users">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="stat-number users" id="totalUsers">0</div>
                            </div>
                            <div class="stat-label">Total Users</div>
                        </div>
                        <div class="stat-card therapists">
                            <div class="stat-header">
                                <div class="stat-icon therapists">
                                    <i class="fas fa-user-md"></i>
                                </div>
                                <div class="stat-number therapists" id="activeTherapists">0</div>
                            </div>
                            <div class="stat-label">Active Therapists</div>
                        </div>
                        <div class="stat-card appointments">
                            <div class="stat-header">
                                <div class="stat-icon appointments">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="stat-number appointments" id="todayAppointments">0</div>
                            </div>
                            <div class="stat-label">Today's Appointments</div>
                        </div>
                        <div class="stat-card revenue">
                            <div class="stat-header">
                                <div class="stat-icon revenue">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-number revenue" id="totalRevenue">$0</div>
                            </div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                    </div>

                    <div class="section-title">Quick Actions</div>
                    <div class="quick-actions">
                        <div class="quick-action-card" onclick="quickBlockUser()">
                            <div class="quick-action-icon block">
                                <i class="fas fa-user-slash"></i>
                            </div>
                            <div>Block User</div>
                        </div>
                        <div class="quick-action-card" onclick="reviewPayments()">
                            <div class="quick-action-icon payment">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div>Review Payments</div>
                        </div>
                    </div>
                </div>

                <!-- Users Section -->
                <div id="usersSection" class="content-section" style="display: none;">
                    <div class="section-title">User Management</div>
                    <div id="usersTable" class="loading">
                        <div class="spinner"></div>
                        Loading users...
                    </div>
                </div>

                <!-- Therapists Section -->
                <div id="therapistsSection" class="content-section" style="display: none;">
                    <div class="section-title">Therapist Management</div>
                    <div id="therapistsTable" class="loading">
                        <div class="spinner"></div>
                        Loading therapists...
                    </div>
                </div>

                <!-- Appointments Section -->
                <div id="appointmentsSection" class="content-section" style="display: none;">
                    <div class="section-title">Appointment Management</div>
                    <div id="appointmentsTable" class="loading">
                        <div class="spinner"></div>
                        Loading appointments...
                    </div>
                </div>

                <!-- Payments Section -->
                <div id="paymentsSection" class="content-section" style="display: none;">
                    <div class="section-title">Payment Management</div>
                    <div id="paymentsTable" class="loading">
                        <div class="spinner"></div>
                        Loading payments...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Exact Flutter Admin Portal Implementation
        const API_BASE_URL = '/api';

        // Admin credentials (exactly like Flutter)
        const ADMIN_EMAIL = "<EMAIL>";
        const ADMIN_PASSWORD = "MindEase@Admin2024";

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Admin portal initializing...');

            // Setup form submission (exactly like Flutter)
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');

            if (loginForm && loginBtn) {
                // Handle form submission
                loginForm.addEventListener('submit', handleLogin);

                // Handle button click
                loginBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleLogin(e);
                });

                console.log('✅ Login handlers attached');
            }

            // Setup password toggle
            setupPasswordToggle();

            console.log('✅ Admin portal ready');
        });

        // Handle login (exactly like Flutter _login method)
        async function handleLogin(e) {
            e.preventDefault();

            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');
            const loginBtn = document.getElementById('loginBtn');
            const errorMessage = document.getElementById('errorMessage');

            if (!emailInput || !passwordInput) {
                console.error('❌ Form inputs not found');
                return;
            }

            const email = emailInput.value.trim();
            const password = passwordInput.value;

            console.log('🔐 Attempting login...');

            // Show loading state (exactly like Flutter)
            loginBtn.disabled = true;
            loginBtn.innerHTML = `
                <div style="display: inline-flex; align-items: center;">
                    <div class="spinner" style="width: 20px; height: 20px; margin-right: 8px; border-width: 2px;"></div>
                    Logging in...
                </div>
            `;
            errorMessage.style.display = 'none';

            try {
                // Simulate network delay (like Flutter)
                await new Promise(resolve => setTimeout(resolve, 800));

                // Simple admin authentication (exactly like Flutter)
                if (email === ADMIN_EMAIL && password === ADMIN_PASSWORD) {
                    console.log('✅ Login successful!');

                    // Add success animation
                    loginBtn.innerHTML = `
                        <div style="display: inline-flex; align-items: center;">
                            <i class="fas fa-check" style="margin-right: 8px;"></i>
                            Login Successful!
                        </div>
                    `;
                    loginBtn.style.background = 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)';

                    // Navigate to admin dashboard (exactly like Flutter)
                    setTimeout(() => {
                        document.getElementById('loginScreen').style.display = 'none';
                        document.getElementById('adminDashboard').style.display = 'block';

                        // Load dashboard data
                        loadDashboardStats();
                    }, 500);

                } else {
                    throw new Error('Invalid admin credentials');
                }
            } catch (error) {
                console.log('❌ Login failed:', error.message);

                // Show error (exactly like Flutter _showErrorDialog)
                errorMessage.textContent = error.message;
                errorMessage.style.display = 'block';

                // Add shake animation to form
                const loginCard = document.querySelector('.login-card');
                loginCard.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    loginCard.style.animation = '';
                }, 500);

            } finally {
                // Reset button (exactly like Flutter)
                setTimeout(() => {
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Login to Dashboard';
                    loginBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                }, 1000);
            }
        }

        // Setup password toggle (exactly like Flutter)
        function setupPasswordToggle() {
            const passwordToggle = document.querySelector('.password-toggle');
            if (passwordToggle) {
                passwordToggle.addEventListener('click', togglePassword);
            }
        }

        // Toggle password visibility (exactly like Flutter)
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.querySelector('.password-toggle i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Show different sections (exactly like Flutter drawer navigation)
        function showSection(section, element) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(el => {
                el.style.display = 'none';
            });

            // Remove active class from all sidebar items
            document.querySelectorAll('.sidebar-item').forEach(el => {
                el.classList.remove('active');
            });

            // Show selected section
            document.getElementById(section + 'Section').style.display = 'block';

            // Add active class to clicked sidebar item
            element.classList.add('active');

            // Update header title (exactly like Flutter)
            const titles = {
                overview: 'Dashboard Overview',
                users: 'User Management',
                therapists: 'Therapist Management',
                appointments: 'Appointment Management',
                payments: 'Payment Management'
            };

            document.getElementById('headerTitle').textContent = titles[section];

            // Load section data
            switch(section) {
                case 'overview':
                    loadDashboardStats();
                    break;
                case 'users':
                    loadUsers();
                    break;
                case 'therapists':
                    loadTherapists();
                    break;
                case 'appointments':
                    loadAppointments();
                    break;
                case 'payments':
                    loadPayments();
                    break;
            }
        }

        // Load dashboard statistics (exactly like Flutter)
        async function loadDashboardStats() {
            console.log('📊 Loading dashboard stats...');

            // Show loading animation on stat cards
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                stat.innerHTML = '<div class="spinner" style="width: 24px; height: 24px; border-width: 2px;"></div>';
            });

            try {
                // Fetch all data in parallel (exactly like Flutter)
                console.log('🔄 Fetching data from API...');
                const [usersRes, therapistsRes, appointmentsRes] = await Promise.all([
                    fetch(API_BASE_URL + '/users'),
                    fetch(API_BASE_URL + '/therapists'),
                    fetch(API_BASE_URL + '/appointments')
                ]);

                if (!usersRes.ok || !therapistsRes.ok || !appointmentsRes.ok) {
                    throw new Error('Failed to fetch data from API');
                }

                const users = await usersRes.json();
                const therapists = await therapistsRes.json();
                const appointments = await appointmentsRes.json();

                console.log('✅ Data loaded:', {
                    users: users.length,
                    therapists: therapists.length,
                    appointments: appointments.length
                });

                // Calculate today's appointments (exactly like Flutter)
                const today = new Date();
                const todayAppointments = appointments.filter(appointment => {
                    if (appointment.appointmentDate) {
                        const appointmentDate = new Date(appointment.appointmentDate);
                        return appointmentDate.getFullYear() === today.getFullYear() &&
                               appointmentDate.getMonth() === today.getMonth() &&
                               appointmentDate.getDate() === today.getDate();
                    }
                    return false;
                }).length;

                // Calculate total revenue (exactly like Flutter)
                let totalRevenue = 0.0;
                appointments.forEach(appointment => {
                    if (appointment.fee) {
                        totalRevenue += parseFloat(appointment.fee);
                    }
                });

                // Animate numbers counting up (fancy effect)
                animateNumber('totalUsers', 0, users.length || 0, 1000);
                animateNumber('activeTherapists', 0, therapists.length || 0, 1200);
                animateNumber('todayAppointments', 0, todayAppointments, 1400);
                animateRevenue('totalRevenue', 0, totalRevenue, 1600);

                console.log('✅ Dashboard stats updated successfully');

                // Add success notification
                showNotification('Dashboard loaded successfully!', 'success');

            } catch (error) {
                console.error('❌ Error loading dashboard stats:', error);

                // Show error state
                statNumbers.forEach(stat => {
                    stat.textContent = 'Error';
                    stat.style.color = '#f44336';
                });

                showNotification('Failed to load dashboard data', 'error');
            }
        }

        // Animate number counting (fancy effect)
        function animateNumber(elementId, start, end, duration) {
            const element = document.getElementById(elementId);
            const startTime = performance.now();

            function updateNumber(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function for smooth animation
                const easeOut = 1 - Math.pow(1 - progress, 3);
                const current = Math.floor(start + (end - start) * easeOut);

                element.textContent = current;

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                }
            }

            requestAnimationFrame(updateNumber);
        }

        // Animate revenue counting (fancy effect)
        function animateRevenue(elementId, start, end, duration) {
            const element = document.getElementById(elementId);
            const startTime = performance.now();

            function updateRevenue(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function for smooth animation
                const easeOut = 1 - Math.pow(1 - progress, 3);
                const current = start + (end - start) * easeOut;

                element.textContent = '$' + current.toFixed(2);

                if (progress < 1) {
                    requestAnimationFrame(updateRevenue);
                }
            }

            requestAnimationFrame(updateRevenue);
        }

        // Show notification (fancy effect)
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 16px 24px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10000;
                font-weight: 500;
                animation: slideInRight 0.3s ease-out;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Load users (exactly like Flutter UserManagementScreen)
        async function loadUsers() {
            console.log('Loading users...');
            const container = document.getElementById('usersTable');

            try {
                const response = await fetch(API_BASE_URL + '/users');
                const users = await response.json();

                console.log('Users loaded:', users.length);

                const table = `
                    <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
                        <div style="position: relative;">
                            <i class="fas fa-search" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: #666;"></i>
                            <input type="text" id="userSearchInput" placeholder="Search users by name or email..."
                                   style="width: 400px; padding: 12px 12px 12px 40px; border: 2px solid #e1e5e9; border-radius: 12px; font-size: 14px; transition: all 0.3s ease;"
                                   onkeyup="filterUsers(this.value)" onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#e1e5e9'">
                        </div>
                        <div style="display: flex; gap: 12px;">
                            <button onclick="loadUsers()" style="padding: 12px 20px; background: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-sync-alt"></i> Refresh
                            </button>
                            <button onclick="exportUsers()" style="padding: 12px 20px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 8px;">
                                <i class="fas fa-download"></i> Export
                            </button>
                        </div>
                    </div>
                    <table style="width: 100%; border-collapse: collapse;" id="usersDataTable">
                        <thead>
                            <tr style="background: #f8f9ff;">
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Username</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Email</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Role</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Status</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Created</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${users.map(user => `
                                <tr data-email="${user.email}" data-username="${user.username || ''}">
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${user.username || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${user.email}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; background: #e3f2fd; color: #1976d2;">
                                            ${user.role || 'user'}
                                        </span>
                                    </td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; background: ${user.isBlocked ? '#ffebee' : '#e8f5e8'}; color: ${user.isBlocked ? '#c62828' : '#2e7d32'};">
                                            ${user.isBlocked ? 'Blocked' : 'Active'}
                                        </span>
                                    </td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${new Date(user.createdAt).toLocaleDateString()}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <button onclick="blockUser('${user._id}', ${user.isBlocked})" style="padding: 6px 12px; background: ${user.isBlocked ? '#4caf50' : '#f44336'}; color: white; border: none; border-radius: 4px; margin-right: 5px; cursor: pointer;">
                                            ${user.isBlocked ? 'Unblock' : 'Block'}
                                        </button>
                                        <button onclick="deleteUser('${user._id}')" style="padding: 6px 12px; background: #ff9800; color: white; border: none; border-radius: 4px; cursor: pointer;">Delete</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                container.innerHTML = table;
                window.allUsers = users; // Store for filtering
            } catch (error) {
                container.innerHTML = '<p style="text-align: center; color: #dc3545; padding: 20px;">Error loading users</p>';
                console.error('Error loading users:', error);
            }
        }

        // Filter users (exactly like Flutter with fancy animations)
        function filterUsers(query) {
            const rows = document.querySelectorAll('#usersDataTable tbody tr');
            const lowerQuery = query.toLowerCase();
            let visibleCount = 0;

            rows.forEach((row, index) => {
                const email = row.getAttribute('data-email').toLowerCase();
                const username = row.getAttribute('data-username').toLowerCase();

                if (email.includes(lowerQuery) || username.includes(lowerQuery)) {
                    row.style.display = '';
                    row.style.animation = `fadeIn 0.3s ease-out ${index * 0.05}s both`;
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });

            // Update search results count
            updateSearchResults(visibleCount, rows.length);
        }

        // Update search results count
        function updateSearchResults(visible, total) {
            let resultDiv = document.getElementById('searchResults');
            if (!resultDiv) {
                resultDiv = document.createElement('div');
                resultDiv.id = 'searchResults';
                resultDiv.style.cssText = 'margin-bottom: 16px; color: #666; font-size: 14px;';
                document.getElementById('usersDataTable').parentNode.insertBefore(resultDiv, document.getElementById('usersDataTable'));
            }

            if (visible === total) {
                resultDiv.textContent = `Showing all ${total} users`;
            } else {
                resultDiv.textContent = `Showing ${visible} of ${total} users`;
            }
        }

        // Export users functionality
        function exportUsers() {
            if (!window.allUsers) {
                showNotification('No user data to export', 'error');
                return;
            }

            // Create CSV content
            const headers = ['Username', 'Email', 'Role', 'Status', 'Created Date'];
            const csvContent = [
                headers.join(','),
                ...window.allUsers.map(user => [
                    user.username || 'N/A',
                    user.email,
                    user.role || 'user',
                    user.isBlocked ? 'Blocked' : 'Active',
                    new Date(user.createdAt).toLocaleDateString()
                ].join(','))
            ].join('\n');

            // Download CSV file
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mindease-users-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showNotification('Users exported successfully!', 'success');
        }

        // Block user (exactly like Flutter with fancy animations)
        async function blockUser(userId, isCurrentlyBlocked) {
            console.log('🔧 Blocking user:', userId, 'Currently blocked:', isCurrentlyBlocked);

            // Show confirmation dialog (exactly like Flutter)
            const action = isCurrentlyBlocked ? 'unblock' : 'block';
            const confirmed = confirm(`Are you sure you want to ${action} this user?`);

            if (!confirmed) return;

            // Find the button and show loading state
            const button = event.target;
            const originalText = button.textContent;
            button.disabled = true;
            button.innerHTML = '<div class="spinner" style="width: 16px; height: 16px; border-width: 2px;"></div>';

            try {
                const response = await fetch(`${API_BASE_URL}/users/${userId}/block`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ isBlocked: !isCurrentlyBlocked })
                });

                if (response.ok) {
                    // Show success notification
                    showNotification(
                        isCurrentlyBlocked ? 'User unblocked successfully!' : 'User blocked successfully!',
                        'success'
                    );

                    // Animate the row
                    const row = button.closest('tr');
                    row.style.animation = 'fadeIn 0.5s ease-out';

                    // Refresh the list with animation
                    setTimeout(() => {
                        loadUsers();
                    }, 500);

                } else {
                    throw new Error('Failed to update user status');
                }
            } catch (error) {
                showNotification('Error updating user status: ' + error.message, 'error');
                console.error('❌ Error blocking user:', error);

                // Reset button
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // Delete user
        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                alert('Delete user functionality would be implemented here: ' + userId);
            }
        }

        // Load therapists (exactly like Flutter TherapistManagementScreen)
        async function loadTherapists() {
            console.log('Loading therapists...');
            const container = document.getElementById('therapistsTable');

            try {
                const response = await fetch(API_BASE_URL + '/therapists');
                const therapists = await response.json();

                console.log('Therapists loaded:', therapists.length);

                const table = `
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9ff;">
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Name</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Email</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Specialization</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Status</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${therapists.map(therapist => `
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${therapist.username || therapist.name || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${therapist.email}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${therapist.specialization || 'General'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; background: #e8f5e8; color: #2e7d32;">
                                            Active
                                        </span>
                                    </td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <button onclick="editTherapist('${therapist._id}')" style="padding: 6px 12px; background: #2196f3; color: white; border: none; border-radius: 4px; margin-right: 5px; cursor: pointer;">Edit</button>
                                        <button onclick="deleteTherapist('${therapist._id}')" style="padding: 6px 12px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">Delete</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                container.innerHTML = table;
            } catch (error) {
                container.innerHTML = '<p style="text-align: center; color: #dc3545; padding: 20px;">Error loading therapists</p>';
                console.error('Error loading therapists:', error);
            }
        }

        // Load appointments (exactly like Flutter AppointmentManagementScreen)
        async function loadAppointments() {
            console.log('Loading appointments...');
            const container = document.getElementById('appointmentsTable');

            try {
                const response = await fetch(API_BASE_URL + '/appointments');
                const appointments = await response.json();

                console.log('Appointments loaded:', appointments.length);

                const table = `
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background: #f8f9ff;">
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">User</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Therapist</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Date</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Time</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Type</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Status</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Fee</th>
                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${appointments.map(apt => `
                                <tr>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.userEmail || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.therapistEmail || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.appointmentDate ? new Date(apt.appointmentDate).toLocaleDateString() : 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.appointmentTime || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">${apt.type || 'N/A'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <span style="padding: 4px 12px; border-radius: 20px; font-size: 12px; background: #fff3cd; color: #856404;">
                                            ${apt.status || 'Pending'}
                                        </span>
                                    </td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">$${apt.fee || '0.00'}</td>
                                    <td style="padding: 12px; border-bottom: 1px solid #eee;">
                                        <button onclick="editAppointment('${apt._id}')" style="padding: 6px 12px; background: #2196f3; color: white; border: none; border-radius: 4px; margin-right: 5px; cursor: pointer;">Edit</button>
                                        <button onclick="cancelAppointment('${apt._id}')" style="padding: 6px 12px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;

                container.innerHTML = table;
            } catch (error) {
                container.innerHTML = '<p style="text-align: center; color: #dc3545; padding: 20px;">Error loading appointments</p>';
                console.error('Error loading appointments:', error);
            }
        }

        // Load payments (exactly like Flutter PaymentManagementScreen)
        async function loadPayments() {
            console.log('Loading payments...');
            const container = document.getElementById('paymentsTable');

            // For now, show a placeholder since payments might not be fully implemented
            container.innerHTML = `
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9ff;">
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Payment ID</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">User</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Amount</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Date</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Status</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                                Payment management coming soon...
                            </td>
                        </tr>
                    </tbody>
                </table>
            `;
        }

        // Action functions (exactly like Flutter)
        function editTherapist(therapistId) {
            alert('Edit therapist functionality would be implemented here: ' + therapistId);
        }

        function deleteTherapist(therapistId) {
            if (confirm('Are you sure you want to delete this therapist?')) {
                alert('Delete therapist functionality would be implemented here: ' + therapistId);
            }
        }

        function editAppointment(appointmentId) {
            alert('Edit appointment functionality would be implemented here: ' + appointmentId);
        }

        function cancelAppointment(appointmentId) {
            if (confirm('Are you sure you want to cancel this appointment?')) {
                alert('Cancel appointment functionality would be implemented here: ' + appointmentId);
            }
        }

        function quickBlockUser() {
            alert('Quick block user functionality would be implemented here');
        }

        function reviewPayments() {
            alert('Review payments functionality would be implemented here');
        }

        function showNotifications() {
            alert('Notifications feature coming soon!');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                document.getElementById('adminDashboard').style.display = 'none';
                document.getElementById('loginScreen').style.display = 'flex';

                // Reset form
                document.getElementById('email').value = '<EMAIL>';
                document.getElementById('password').value = 'MindEase@Admin2024';
                document.getElementById('errorMessage').style.display = 'none';
            }
        }

        // Initialize on page load
        console.log('Admin portal loaded and ready');
        console.log('Admin credentials:', ADMIN_EMAIL, ADMIN_PASSWORD);

        // Test that elements exist
        window.addEventListener('load', function() {
            console.log('Page fully loaded');
            console.log('Login screen element:', document.getElementById('loginScreen'));
            console.log('Dashboard element:', document.getElementById('adminDashboard'));
            console.log('Email input:', document.getElementById('email'));
            console.log('Password input:', document.getElementById('password'));

            // Show ready message
            setTimeout(() => {
                console.log('🎉 Admin portal is ready! Try clicking the login buttons.');
            }, 1000);
        });
    </script>
</body>
</html>
