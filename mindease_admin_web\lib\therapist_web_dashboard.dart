import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'theme/app_theme.dart';
import 'admin_login.dart';

class TherapistWebDashboard extends StatefulWidget {
  final String therapistId;
  final Map<String, dynamic> therapistData;

  const TherapistWebDashboard({
    super.key,
    required this.therapistId,
    required this.therapistData,
  });

  @override
  State<TherapistWebDashboard> createState() => _TherapistWebDashboardState();
}

class _TherapistWebDashboardState extends State<TherapistWebDashboard> {
  int _selectedIndex = 0;
  List<Map<String, dynamic>> _appointments = [];
  List<Map<String, dynamic>> _patients = [];
  bool _isLoading = true;
  Map<String, dynamic> _stats = {
    'totalPatients': 0,
    'todayAppointments': 0,
    'completedSessions': 0,
    'pendingApprovals': 0,
  };

  @override
  void initState() {
    super.initState();
    _fetchDashboardData();
  }

  Future<void> _fetchDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Fetch appointments for this therapist
      final appointmentsResponse = await http.get(
        Uri.parse('http://192.168.1.9:3000/api/appointments'),
        headers: {'Content-Type': 'application/json'},
      );

      if (appointmentsResponse.statusCode == 200) {
        final List<dynamic> allAppointments =
            jsonDecode(appointmentsResponse.body);
        _appointments = allAppointments
            .where((apt) => apt['therapistId'] == widget.therapistId)
            .cast<Map<String, dynamic>>()
            .toList();

        // Calculate stats
        final today = DateTime.now();
        final todayAppointments = _appointments.where((apt) {
          if (apt['appointmentDate'] != null) {
            final appointmentDate = DateTime.parse(apt['appointmentDate']);
            return appointmentDate.year == today.year &&
                appointmentDate.month == today.month &&
                appointmentDate.day == today.day;
          }
          return false;
        }).length;

        final completedSessions =
            _appointments.where((apt) => apt['status'] == 'completed').length;

        setState(() {
          _stats = {
            'totalPatients':
                _appointments.map((apt) => apt['userId']).toSet().length,
            'todayAppointments': todayAppointments,
            'completedSessions': completedSessions,
            'pendingApprovals':
                _appointments.where((apt) => apt['status'] == 'pending').length,
          };
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error fetching dashboard data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            'Therapist Dashboard - ${widget.therapistData['username'] ?? 'Therapist'}'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                    builder: (context) => const AdminLoginScreen()),
              );
            },
          ),
        ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.backgroundGradient,
        ),
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(AppTheme.spacingM),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Header
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(AppTheme.spacingL),
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        borderRadius: BorderRadius.circular(AppTheme.radiusL),
                        boxShadow: AppTheme.mediumShadow,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding:
                                    const EdgeInsets.all(AppTheme.spacingM),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius:
                                      BorderRadius.circular(AppTheme.radiusM),
                                ),
                                child: const Icon(
                                  Icons.psychology,
                                  color: Colors.white,
                                  size: 32,
                                ),
                              ),
                              const SizedBox(width: AppTheme.spacingM),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Welcome, Dr. ${widget.therapistData['username'] ?? 'Therapist'}',
                                      style: AppTheme.headingMedium.copyWith(
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Specialization: ${widget.therapistData['specialization'] ?? 'General Therapy'}',
                                      style: AppTheme.bodyMedium.copyWith(
                                        color: Colors.white.withOpacity(0.9),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacingL),

                    // Statistics Cards
                    Text(
                      'Dashboard Overview',
                      style: AppTheme.headingMedium,
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      crossAxisSpacing: AppTheme.spacingM,
                      mainAxisSpacing: AppTheme.spacingM,
                      childAspectRatio: 1.5,
                      children: [
                        _buildStatCard(
                          'Total Patients',
                          _stats['totalPatients'].toString(),
                          Icons.people,
                          AppTheme.primaryColor,
                        ),
                        _buildStatCard(
                          'Today\'s Appointments',
                          _stats['todayAppointments'].toString(),
                          Icons.calendar_today,
                          AppTheme.warningColor,
                        ),
                        _buildStatCard(
                          'Completed Sessions',
                          _stats['completedSessions'].toString(),
                          Icons.check_circle,
                          AppTheme.successColor,
                        ),
                        _buildStatCard(
                          'Pending Approvals',
                          _stats['pendingApprovals'].toString(),
                          Icons.pending,
                          AppTheme.errorColor,
                        ),
                      ],
                    ),
                    const SizedBox(height: AppTheme.spacingL),

                    // Recent Appointments
                    Text(
                      'Recent Appointments',
                      style: AppTheme.headingMedium,
                    ),
                    const SizedBox(height: AppTheme.spacingM),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(AppTheme.radiusL),
                        boxShadow: AppTheme.softShadow,
                      ),
                      child: _appointments.isEmpty
                          ? const Padding(
                              padding: EdgeInsets.all(AppTheme.spacingXL),
                              child: Center(
                                child: Text('No appointments found'),
                              ),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: _appointments.take(5).length,
                              itemBuilder: (context, index) {
                                final appointment = _appointments[index];
                                return _buildAppointmentTile(appointment);
                              },
                            ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppTheme.radiusL),
        boxShadow: AppTheme.softShadow,
        border: Border.all(color: color.withOpacity(0.1)),
      ),
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacingL),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppTheme.radiusL),
          gradient: LinearGradient(
            colors: [color.withOpacity(0.05), Colors.white],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.all(AppTheme.spacingM),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusM),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                Text(
                  value,
                  style: AppTheme.headingMedium.copyWith(
                    color: color,
                    fontSize: 32,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingM),
            Text(
              title,
              style: AppTheme.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentTile(Map<String, dynamic> appointment) {
    final status = appointment['status'] ?? 'pending';
    final userEmail = appointment['userEmail'] ?? 'N/A';
    final appointmentDate = appointment['appointmentDate'] != null
        ? DateTime.parse(appointment['appointmentDate']).toLocal()
        : null;
    final appointmentTime = appointment['appointmentTime'] ?? 'N/A';

    Color statusColor;
    switch (status.toLowerCase()) {
      case 'confirmed':
        statusColor = Colors.green;
        break;
      case 'cancelled':
        statusColor = Colors.red;
        break;
      case 'completed':
        statusColor = Colors.blue;
        break;
      default:
        statusColor = Colors.orange;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(left: BorderSide(color: statusColor, width: 4)),
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Patient: $userEmail',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                if (appointmentDate != null)
                  Text(
                      'Date: ${appointmentDate.day}/${appointmentDate.month}/${appointmentDate.year}'),
                Text('Time: $appointmentTime'),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              status.toUpperCase(),
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: statusColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
