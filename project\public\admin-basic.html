<!DOCTYPE html>
<html>
<head>
    <title>MindEase Admin Portal</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f0f0f0; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 0 10px rgba(0,0,0,0.1); 
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
            color: #333; 
        }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin-bottom: 30px; 
        }
        .stat-card { 
            background: #667eea; 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center; 
        }
        .stat-number { 
            font-size: 36px; 
            font-weight: bold; 
            margin-bottom: 10px; 
        }
        .section { 
            margin-bottom: 30px; 
        }
        .section-title { 
            font-size: 24px; 
            font-weight: bold; 
            margin-bottom: 15px; 
            color: #333; 
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 10px; 
        }
        th, td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid #ddd; 
        }
        th { 
            background: #f8f9fa; 
            font-weight: bold; 
        }
        .btn { 
            padding: 6px 12px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            margin-right: 5px; 
        }
        .btn-primary { 
            background: #007bff; 
            color: white; 
        }
        .btn-danger { 
            background: #dc3545; 
            color: white; 
        }
        .refresh-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 MindEase Admin Portal</h1>
            <p>Administrative Dashboard</p>
            <button class="refresh-btn" onclick="window.location.reload()">🔄 Refresh Data</button>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">Loading...</div>
                <div>Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalTherapists">Loading...</div>
                <div>Total Therapists</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalAppointments">Loading...</div>
                <div>Total Appointments</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalRevenue">Loading...</div>
                <div>Total Revenue</div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Users</div>
            <div id="usersTable">Loading users...</div>
        </div>

        <div class="section">
            <div class="section-title">Therapists</div>
            <div id="therapistsTable">Loading therapists...</div>
        </div>

        <div class="section">
            <div class="section-title">Appointments</div>
            <div id="appointmentsTable">Loading appointments...</div>
        </div>
    </div>

    <script>
        console.log('Admin portal loaded');

        // Load data immediately when page loads
        loadAllData();

        function loadAllData() {
            console.log('Loading all data...');
            loadUsers();
            loadTherapists();
            loadAppointments();
        }

        function loadUsers() {
            console.log('Loading users...');
            fetch('/api/users')
                .then(response => {
                    console.log('Users response:', response.status);
                    return response.json();
                })
                .then(users => {
                    console.log('Users data:', users);
                    document.getElementById('totalUsers').textContent = users.length;
                    
                    let html = '<table><tr><th>Username</th><th>Email</th><th>Created</th><th>Actions</th></tr>';
                    users.forEach(user => {
                        html += `<tr>
                            <td>${user.username || 'N/A'}</td>
                            <td>${user.email}</td>
                            <td>${new Date(user.createdAt).toLocaleDateString()}</td>
                            <td>
                                <button class="btn btn-primary" onclick="alert('Edit user: ${user._id}')">Edit</button>
                                <button class="btn btn-danger" onclick="if(confirm('Delete user?')) alert('Delete user: ${user._id}')">Delete</button>
                            </td>
                        </tr>`;
                    });
                    html += '</table>';
                    document.getElementById('usersTable').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading users:', error);
                    document.getElementById('usersTable').innerHTML = 'Error loading users: ' + error.message;
                    document.getElementById('totalUsers').textContent = 'Error';
                });
        }

        function loadTherapists() {
            console.log('Loading therapists...');
            fetch('/api/therapists')
                .then(response => {
                    console.log('Therapists response:', response.status);
                    return response.json();
                })
                .then(therapists => {
                    console.log('Therapists data:', therapists);
                    document.getElementById('totalTherapists').textContent = therapists.length;
                    
                    let html = '<table><tr><th>Name</th><th>Email</th><th>Specialization</th><th>Actions</th></tr>';
                    therapists.forEach(therapist => {
                        html += `<tr>
                            <td>${therapist.username || therapist.name || 'N/A'}</td>
                            <td>${therapist.email}</td>
                            <td>${therapist.specialization || 'General'}</td>
                            <td>
                                <button class="btn btn-primary" onclick="alert('Edit therapist: ${therapist._id}')">Edit</button>
                                <button class="btn btn-danger" onclick="if(confirm('Delete therapist?')) alert('Delete therapist: ${therapist._id}')">Delete</button>
                            </td>
                        </tr>`;
                    });
                    html += '</table>';
                    document.getElementById('therapistsTable').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading therapists:', error);
                    document.getElementById('therapistsTable').innerHTML = 'Error loading therapists: ' + error.message;
                    document.getElementById('totalTherapists').textContent = 'Error';
                });
        }

        function loadAppointments() {
            console.log('Loading appointments...');
            fetch('/api/appointments')
                .then(response => {
                    console.log('Appointments response:', response.status);
                    return response.json();
                })
                .then(appointments => {
                    console.log('Appointments data:', appointments);
                    document.getElementById('totalAppointments').textContent = appointments.length;
                    
                    // Calculate revenue
                    let totalRevenue = 0;
                    appointments.forEach(apt => {
                        if (apt.fee) totalRevenue += parseFloat(apt.fee);
                    });
                    document.getElementById('totalRevenue').textContent = '$' + totalRevenue.toFixed(2);
                    
                    let html = '<table><tr><th>User</th><th>Therapist</th><th>Date</th><th>Status</th><th>Fee</th><th>Actions</th></tr>';
                    appointments.forEach(apt => {
                        html += `<tr>
                            <td>${apt.userEmail || 'N/A'}</td>
                            <td>${apt.therapistEmail || 'N/A'}</td>
                            <td>${apt.appointmentDate ? new Date(apt.appointmentDate).toLocaleDateString() : 'N/A'}</td>
                            <td>${apt.status || 'Pending'}</td>
                            <td>$${apt.fee || '0.00'}</td>
                            <td>
                                <button class="btn btn-primary" onclick="alert('Edit appointment: ${apt._id}')">Edit</button>
                                <button class="btn btn-danger" onclick="if(confirm('Cancel appointment?')) alert('Cancel appointment: ${apt._id}')">Cancel</button>
                            </td>
                        </tr>`;
                    });
                    html += '</table>';
                    document.getElementById('appointmentsTable').innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading appointments:', error);
                    document.getElementById('appointmentsTable').innerHTML = 'Error loading appointments: ' + error.message;
                    document.getElementById('totalAppointments').textContent = 'Error';
                    document.getElementById('totalRevenue').textContent = 'Error';
                });
        }

        // Test if JavaScript is working
        console.log('JavaScript is working!');
        
        // Show a simple alert to confirm the page loaded
        setTimeout(() => {
            console.log('Page fully loaded, data should be visible');
        }, 2000);
    </script>
</body>
</html>
