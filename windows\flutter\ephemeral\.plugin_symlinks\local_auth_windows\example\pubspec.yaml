name: local_auth_windows_example
description: Demonstrates how to use the local_auth_windows plugin.
publish_to: none

environment:
  sdk: ^3.2.0
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  local_auth_platform_interface: ^1.0.0
  local_auth_windows:
    # When depending on this package from a real application you should use:
    #   local_auth_windows: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ../

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true
